package com.kbao.kbcelms.controller.opportunity;

import com.github.pagehelper.PageInfo;
import com.kbao.commons.exception.BusinessException;
import com.kbao.commons.web.PageRequest;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.annotations.AppApiCheckAnnotation;
import com.kbao.kbcelms.common.model.OperationLogRequest;
import com.kbao.kbcelms.enums.RoleTypeEnum;
import com.kbao.kbcelms.opportunity.entity.Opportunity;
import com.kbao.kbcelms.opportunityteam.model.OpportunityTeamMember;
import com.kbao.kbcelms.opportunityteam.service.OpportunityTeamService;
import com.kbao.kbcelms.opportunityteamdivision.model.OpportunityTeamDivisionVO;
import com.kbao.kbcelms.opportunityteamdivision.service.OpportunityTeamDivisionService;
import com.kbao.tool.model.SysUser;
import com.kbao.tool.util.SysLoginUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

//@AppApiCheckAnnotation(code = "elmsWeb")
@Slf4j
@RestController
@RequestMapping("/api/opportunity/team")
@Api(tags = "机会项目成员相关接口")
public class OpportunityTeamController {

    @Autowired
    private OpportunityTeamService opportunityTeamService;

    @Autowired
    private OpportunityTeamDivisionService opportunityTeamDivisionService;

//    public String getAppCode() {
//        return this.getClass().getAnnotation(AppApiCheckAnnotation.class).code();
//    }


    /**
     * 项目成员与分工初始化
     */
    @ApiOperation(value = "项目成员与分工初始化", notes = "项目成员与分工初始化")
    @PostMapping("/init")
    public Result init(@RequestBody List<OpportunityTeamMember> members){
        opportunityTeamService.init(members,SysLoginUtils.getUser().getTenantId());
        return Result.succeed("操作成功");
    }

    /**
     * 项目成员列表
     */
    @ApiOperation(value = "项目成员列表", notes = "项目成员列表")
    @PostMapping("/list")
    public Result<List<OpportunityTeamMember>> list(@RequestBody OpportunityTeamMember member){
        List<OpportunityTeamMember>  result = opportunityTeamService.selectMember(member.getOpportunityId(), SysLoginUtils.getUser().getTenantId());

        return Result.succeed(result,"查询成功");
    }

    /**
     * 项目成员删除
     */
    @ApiOperation(value = "项目成员删除", notes = "项目成员删除")
    @PostMapping("/remove")
    public Result remove(@RequestBody OpportunityTeamMember member) {
        opportunityTeamService.remove(member.getId(),SysLoginUtils.getUser().getUserId());
        return Result.succeed("操作成功");
    }

    /**
     * 项目成员添加
     */
    @ApiOperation(value = "项目成员添加", notes = "项目成员添加")
    @PostMapping("/add")
    public Result add(@RequestBody OpportunityTeamMember member) {

        if(member.getRoleType() == RoleTypeEnum.BRANCH_PM.getCode()
                || member.getRoleType() == RoleTypeEnum.HEAD_PM.getCode()
                ||member.getRoleType()==RoleTypeEnum.BRANCH_CLERK.getCode()){
            throw new BusinessException("不能新增项目经理");
        }

        opportunityTeamService.add(member,SysLoginUtils.getUser().getUserId(), SysLoginUtils.getUser().getTenantId(),false);
        return Result.succeed("操作成功");
    }

    /**
     * 项目成员添加
     */
    @ApiOperation(value = "项目成员确认或拒绝", notes = "项目成员确认或拒绝")
    @PostMapping("/acceptOrReject")
    public Result acceptOrReject(@RequestBody OpportunityTeamMember member) {
        opportunityTeamService.acceptOrReject(member.getOpportunityId(),member.getJoinType(),SysLoginUtils.getUser().getUserId(),SysLoginUtils.getUser().getTenantId());
        return Result.succeed("操作成功");
    }

    /**
     * 项目成员确认邀请
     */
    @ApiOperation(value = "项目成员确认邀请", notes = "项目成员确认邀请")
    @PostMapping("/invite")
    public Result inviteNotice(@RequestBody OpportunityTeamMember member) {
        SysUser user = SysLoginUtils.getUser();
        opportunityTeamService.inviteNotice(member.getId(),user.getNickName(),user.getTenantId(),user.getUserId(),"elmsWeb");
        return Result.succeed("操作成功");
    }

    @ApiOperation(value = "项目分工添加", notes = "项目分工添加")
    @PostMapping("/division/add")
    public Result addDivision(@RequestBody OpportunityTeamDivisionVO vo) {
        SysUser user = SysLoginUtils.getUser();
        try {
            opportunityTeamDivisionService.add(vo,user.getUserId(),user.getTenantId());
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            return Result.failed(e.getMessage());
        }
        return Result.succeed("操作成功");
    }

    @ApiOperation(value = "项目分工修改", notes = "项目分工修改")
    @PostMapping("/division/update")
    public Result updateDivision(@RequestBody OpportunityTeamDivisionVO vo) {
        SysUser user = SysLoginUtils.getUser();
        opportunityTeamDivisionService.update(vo,user.getUserId(),user.getTenantId());
        return Result.succeed("操作成功");
    }

    @ApiOperation(value = "项目分工列表", notes = "项目分工列表")
    @PostMapping("/division/list")
    public Result<List<OpportunityTeamDivisionVO>> findDivision(@RequestBody OpportunityTeamDivisionVO vo) {
        SysUser user = SysLoginUtils.getUser();
        List<OpportunityTeamDivisionVO> list = opportunityTeamDivisionService.findDivision(user.getTenantId(),vo.getOpportunityId(),vo.getNum());
        return Result.succeed(list,"操作成功");
    }

    @ApiOperation(value = "项目分工详情", notes = "项目分工详情")
    @PostMapping("/division/get")
    public Result<OpportunityTeamDivisionVO> findOneDivision(@RequestBody OpportunityTeamDivisionVO vo) {
        SysUser user = SysLoginUtils.getUser();
        OpportunityTeamDivisionVO result = opportunityTeamDivisionService.findOneDivision(user.getTenantId(),vo.getId());
        return Result.succeed(result,"操作成功");
    }

    @ApiOperation(value = "项目分工发送确认分工比例邮件", notes = "项目分工发送确认分工比例邮件")
    @PostMapping("/division/send")
    public Result send(@RequestBody OpportunityTeamDivisionVO vo) {
        SysUser user = SysLoginUtils.getUser();
        opportunityTeamDivisionService.sendNotice(vo,user.getTenantId(),user.getNickName());
        return Result.succeed("操作成功");
    }

    /**
     * 项目分工确认或拒绝
     */
    @ApiOperation(value = "项目分工确认或拒绝", notes = "项目分工确认或拒绝")
    @PostMapping("/division/acceptOrReject")
    public Result divisionAcceptOrReject(@RequestBody OpportunityTeamDivisionVO vo) {
        opportunityTeamDivisionService.acceptOrReject(vo.getId(),vo.getStatus(),SysLoginUtils.getUser().getUserId());
        return Result.succeed("操作成功");
    }

    /**
     * 项目分工默认配置
     */
    @ApiOperation(value = "项目分工默认配置", notes = "项目分工默认配置")
    @PostMapping("/division/config")
    public Result saveDivisionConfig(@RequestBody OpportunityTeamDivisionVO vo) {
        opportunityTeamDivisionService.setDivisionConfig(vo.getRemark());
        return Result.succeed("操作成功");
    }

    /**
     * 项目负责人变更
     * @return
     */
    @ApiOperation(value = "项目负责人变更", notes = "项目负责人变更")
    @PostMapping("/manager/change")
    public Result changeManager(@RequestBody OperationLogRequest<List<OpportunityTeamMember>> request) {
        // 变更项目经理列表
        List<OpportunityTeamMember> managerList = request.getData();
        //变更备注
        String changeRemark = request.getChangeRemark();

        opportunityTeamService.changeManager(managerList,
                SysLoginUtils.getUser().getUserId(),
                SysLoginUtils.getUser().getTenantId(),
                changeRemark);
        return Result.succeed("操作成功");
    }

}
