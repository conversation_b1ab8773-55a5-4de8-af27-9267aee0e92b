package com.kbao.kbcelms.opportunity.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**
 * 机会待办任务VO
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@ApiModel(description = "机会待办任务VO")
public class OpportunityTodoVO {
    /** 机会ID */
    @ApiModelProperty(value = "机会ID", example = "1")
    private Integer opportunityId;
    
    /** 机会名称 */
    @ApiModelProperty(value = "机会名称", example = "某公司保险项目")
    private String opportunityName;
    
    /** 服务顾问 */
    @ApiModelProperty(value = "服务顾问", example = "张三")
    private String agentName;
    
    /** 所属机构 */
    @ApiModelProperty(value = "所属机构", example = "某保险公司")
    private String companyName;
    
    /** 所属营业部 */
    @ApiModelProperty(value = "所属营业部", example = "北京营业部")
    private String salesCenterName;
    
    /** 企业名称 */
    @ApiModelProperty(value = "企业名称", example = "某企业有限公司")
    private String enterpriseName;
    
    /** 社会统一信用代码 */
    @ApiModelProperty(value = "社会统一信用代码", example = "91110000123456789X")
    private String creditCode;
    
    /** 客户需求 */
    @ApiModelProperty(value = "客户需求", example = "员工意外险")
    private String generalInsuranceType;
    
    /** 是否需要投标 0-否，1-是 */
    @ApiModelProperty(value = "是否需要投标", example = "1", notes = "0-否，1-是")
    private Integer isBid;
    
    /** 提交时间 */
    @ApiModelProperty(value = "提交时间", example = "2025-01-01 10:00:00")
    private Date submitTime;
    
    /** 机会状态 0-待提交，1-已提交，2-锁定，3-中止，4-终止 */
    @ApiModelProperty(value = "机会状态", example = "1", notes = "0-待提交，1-已提交，2-锁定，3-中止，4-终止")
    private Integer status;
    
    /** 预估投保人数 */
    @ApiModelProperty(value = "预估投保人数", example = "100")
    private Integer insureNum;
    
    /** 保费预算 */
    @ApiModelProperty(value = "保费预算", example = "50000")
    private Integer premiumBudget;

    /** 流程步骤 */
    @ApiModelProperty(value = "流程步骤", example = "审核中")
    private String processStep;

    /** 机会类型: 1-员服，2-综合 */
    @ApiModelProperty(value = "机会类型", example = "1", notes = "1-员服，2-综合")
    private String opportunityType;

    /** 机会关闭原因类型：1-机会已成交，2-机会推进失败，3-无效机会 */
    @ApiModelProperty(value = "机会关闭原因类型", example = "1", notes = "1-机会已成交，2-机会推进失败，3-无效机会")
    private Integer closeReasonType;
    
    /** 参与状态 0-待确认，1-已确认，5-已拒绝 */
    @ApiModelProperty(value = "参与状态", example = "0", notes = "0-待确认，1-已确认，5-已拒绝")
    private Integer participationStatus;
    
    /** 项目分工 */
    @ApiModelProperty(value = "项目分工", example = "技术顾问")
    private String divisionId;
    
    /** 分工比例 */
    @ApiModelProperty(value = "分工比例", example = "30.00")
    private String divisionRatio;
} 