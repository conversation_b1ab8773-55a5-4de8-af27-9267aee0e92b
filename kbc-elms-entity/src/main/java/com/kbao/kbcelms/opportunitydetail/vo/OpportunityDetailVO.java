package com.kbao.kbcelms.opportunitydetail.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 机会明细
 */
@Data
@ApiModel(description = "机会明细VO")
public class OpportunityDetailVO {
    /** 主键 */
    private Integer id;
    /** 机会ID */
    private Integer opportunityId;
    /** 投保人员规模 */
    private Integer insureNum;
    /** 是否有历史保单：0-否，1-是 */
    private String hasHistoryPolicy;
    /** 保单到期日期 */
    private Date policyExpireTime;
    /** 是否需要投标：0-否，1-是 */
    private Integer isBid;
    /** 投标结果 0-失败 1-成功 */
    private Integer bidResult;
    /** 投标开始时间 */
    private Date bidStartDate;
    /** 投标结束时间 */
    private Date bidEndDate;
    /** 保费预算 */
    private Integer premiumBudget;
    /** 企业对接人 */
    private String contacter;
    /** 企业对接人职务 */
    private String contacterPost;
    /** 是否添加健康服务产品 */
    private String addHealthService;
    /** 是否添加救援服务产品 */
    private String addRescueService;
    /** 综合险种名称，多个用逗号分割 */
    private String generalInsuranceName;
    /** 备注 */
    private String remark;

    @ApiModelProperty(value = "是否附加生态服务：0-否，1-是", example = "1", notes = "0-否，1-是")
    private Integer attchService;
}
