package com.kbao.kbcelms.controller.base;

import com.kbao.commons.enums.ResultStatusEnum;
import com.kbao.commons.web.Result;
import com.kbao.kbcbsc.log.annotation.LogAnnotation;
import com.kbao.kbcelms.bascode.service.BasCodeService;
import com.kbao.kbcelms.bascode.vo.BaseCodeTreeVO;
import com.kbao.kbcelms.enterprise.base.bean.EnterpriseSearchVo;
import com.kbao.kbcelms.enterprise.type.bean.EnterpriseTypeEnumVO;
import com.kbao.kbcelms.enterprise.type.service.EnterpriseTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/api/base")
@Api(tags = "基础字典API")
public class BaseController {

    @Autowired
    private BasCodeService basCodeService;

    @Autowired
    private EnterpriseTypeService enterpriseTypeService;

    @ApiOperation(value = "查询区域信息", notes = "查询区域信息")
    @PostMapping("/cities")
    @LogAnnotation(module = "基础字典API", recordRequestParam = true, action = "查询", desc = "查询区域信息")
    public Result<List<BaseCodeTreeVO>> getCities(@RequestBody EnterpriseSearchVo vo) {
        List<BaseCodeTreeVO> tree = basCodeService.getBaseCodeTree();
        return Result.succeed(tree, ResultStatusEnum.SUCCESS.getMsg());
    }

    @ApiOperation(value = "查询行业类型", notes = "查询行业类型")
    @PostMapping("/enterpriseTypes")
    @LogAnnotation(module = "基础字典API", recordRequestParam = true, action = "查询", desc = "查询行业类型")
    public Result<List<EnterpriseTypeEnumVO>> getEnterpriseTypes(@RequestBody EnterpriseSearchVo vo) {
        List<EnterpriseTypeEnumVO> list = enterpriseTypeService.getEnterpriseTypeEnum();
        return Result.succeed(list, ResultStatusEnum.SUCCESS.getMsg());
    }


}
