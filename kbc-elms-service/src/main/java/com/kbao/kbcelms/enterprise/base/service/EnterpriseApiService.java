package com.kbao.kbcelms.enterprise.base.service;

import com.kbao.commons.web.Result;
import com.kbao.kbcelms.enterprise.base.model.EnterpriseBasicInfo;
import com.kbao.kbcelms.enterprise.query.config.model.EnterpriseQueryConfig;
import com.kbao.kbcelms.enterprise.query.config.service.EnterpriseQueryConfigService;
import com.kbao.kbcelms.enterprise.query.record.service.EnterpriseQueryRecordService;
import com.kbao.kbcelms.genAgentEnterprise.entity.GenAgentEnterprise;import com.kbao.kbcelms.genAgentEnterprise.service.GenAgentEnterpriseService;import com.kbao.kbcucs.context.RequestContext;import com.kbao.kbcucs.user.model.UserInfoResp;import com.kbao.tool.util.DateUtils;import com.kbao.tool.util.SysLoginUtils;
import org.joda.time.DateTime;import org.joda.time.LocalDate;import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
public class EnterpriseApiService {

    @Autowired
    private TianyanchaService tianyanchaService;
    @Autowired
    private EnterpriseBasicInfoService enterpriseBasicInfoService;
    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;

    @Autowired
    private EnterpriseQueryConfigService enterpriseQueryConfigService;

    @Autowired
    private EnterpriseQueryRecordService enterpriseQueryRecordService;


    /**
     * 企业验真接口
     * @param enterpriseName 企业名称
     * @return 验真结果
     * errorCode: 1：当日查询次数已用完, 2-本月查询次数已用完, 3-未查询到数据
     */
    public Map<String, Object> verify(String enterpriseName) {
        Map<String, Object> result = new HashMap<>();
        UserInfoResp userInfo = RequestContext.UserInfoResp.get();
        // 获取用户的查询限制配置
        EnterpriseQueryConfig config = enterpriseQueryConfigService.getQueryConfigByAgentCode(userInfo.getAgentCode());
        // 检查当日查询次数
        long todayCount = enterpriseQueryRecordService.getTodayQueryCount(userInfo.getAgentCode());
        if (todayCount >= config.getDailyLimit()) {
            result.put("errorCode", 1);
            result.put("dailyLimit", config.getDailyLimit());
            result.put("monthlyLimit", config.getMonthlyLimit());
            return result;
        }
        // 检查本月查询次数
        long monthCount = enterpriseQueryRecordService.getMonthQueryCount(userInfo.getAgentCode());
        if (monthCount >= config.getMonthlyLimit()) {
            result.put("errorCode", 2);
            result.put("monthlyLimit", config.getMonthlyLimit());
            return result;
        }
        //todo 检查行业限制

        EnterpriseBasicInfo basicInfo = enterpriseBasicInfoService.queryByFullName(enterpriseName);
        if (basicInfo == null
            || LocalDate.fromDateFields(basicInfo.getUpdateTime()).plusYears(1).isBefore(LocalDate.now())) {
            basicInfo = tianyanchaService.syncEnterpriseAllInfo(enterpriseName);
        }
        // 记录查询日志
        enterpriseQueryRecordService.recordQueryLog(enterpriseName, userInfo, basicInfo, true, false);
        if (basicInfo == null) {
            result.put("errorCode", 3);
            return result;
        }
        GenAgentEnterprise enterprise = genAgentEnterpriseService.toGenAgentEnterprise(basicInfo);
        result.put("data", enterprise);
        return result;
    }
}
