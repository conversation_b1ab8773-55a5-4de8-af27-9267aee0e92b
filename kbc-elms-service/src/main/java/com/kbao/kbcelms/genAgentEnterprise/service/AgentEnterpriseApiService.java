package com.kbao.kbcelms.genAgentEnterprise.service;

import com.github.pagehelper.PageInfo;import com.kbao.commons.web.PageRequest;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListReqVo;import com.kbao.kbcelms.genAgentEnterprise.bean.AgentEnterpriseListResVo;import org.springframework.beans.factory.annotation.Autowired;import org.springframework.stereotype.Service;
@Service
public class AgentEnterpriseApiService {
    @Autowired
    private GenAgentEnterpriseService genAgentEnterpriseService;

    public PageInfo<AgentEnterpriseListResVo> getAgentEnterpriseList(PageRequest<AgentEnterpriseListReqVo> pageRequest) {
        return genAgentEnterpriseService.getAgentEnterpriseList(pageRequest);
    }
}
