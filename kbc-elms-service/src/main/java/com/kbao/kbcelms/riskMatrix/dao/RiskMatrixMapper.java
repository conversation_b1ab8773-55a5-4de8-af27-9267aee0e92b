package com.kbao.kbcelms.riskMatrix.dao;

import com.kbao.kbcbsc.dao.sql.BaseMapper;
import com.kbao.kbcelms.riskMatrix.bean.RiskMatrixQuery;
import com.kbao.kbcelms.riskMatrix.entity.RiskMatrix;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 风险矩阵Mapper接口
 * 
 * <AUTHOR>
 * @since 2025-01-08
 */
public interface RiskMatrixMapper extends BaseMapper<RiskMatrix, Long> {
    
    /**
     * 根据编码查询风险矩阵
     * 
     * @param code 矩阵编码
     * @return 风险矩阵
     */
    RiskMatrix selectByCode(@Param("code") String code);
    
    /**
     * 根据查询条件分页查询风险矩阵列表
     * 
     * @param query 查询条件
     * @return 风险矩阵列表
     */
    List<RiskMatrix> selectByQuery(RiskMatrixQuery query);
    
    /**
     * 根据查询条件统计总数
     * 
     * @param query 查询条件
     * @return 总数
     */
    Long countByQuery(RiskMatrixQuery query);
    

}
